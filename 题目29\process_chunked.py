import pandas as pd
import os
from typing import List, Dict, Optional
import numpy as np

# 修正文件路径为相对路径
file_path = os.path.join(os.path.dirname(__file__), '原油期权日行情历史数据20250721.xlsx')

class ChunkedExcelProcessor:
    """分块处理Excel数据的类"""
    
    def __init__(self, file_path: str, chunk_size: int = 10000):
        """
        初始化处理器
        
        Args:
            file_path: Excel文件路径
            chunk_size: 每个分块的行数，默认10000行
        """
        self.file_path = file_path
        self.chunk_size = chunk_size
        self.indicators = []
        self.col_map = {
            '成交金额': '成交额（万）',
            '成交量': '成交量',
            '持仓量': '持仓量',
            '行权量': '行权量',
        }
        
    def read_demand_indicators(self) -> List[str]:
        """读取需求表首行，获取指标列表"""
        print("正在读取需求指标...")
        demand_sheet = 0  # 第 1 个 sheet
        row0 = pd.read_excel(self.file_path, sheet_name=demand_sheet, nrows=1)
        
        # 收集"包含指标*"列
        indicators = []
        for c in row0.columns:
            if c.startswith('包含指标') and pd.notna(row0[c].iloc[0]):
                value = row0[c].iloc[0]
                if isinstance(value, str):
                    indicators.append(value.strip())
                else:
                    indicators.append(str(value).strip())
        
        self.indicators = indicators
        print(f"找到指标: {indicators}")
        return indicators
    
    def get_total_rows(self, sheet_name: str) -> int:
        """获取指定sheet的总行数"""
        print(f"正在计算 {sheet_name} 的总行数...")
        # 读取第一列来计算总行数，避免加载所有数据
        temp_df = pd.read_excel(self.file_path, sheet_name=sheet_name, usecols=[0])
        total_rows = len(temp_df)
        print(f"总行数: {total_rows}")
        return total_rows
    
    def process_chunk(self, chunk_df: pd.DataFrame) -> pd.DataFrame:
        """处理单个数据块"""
        # ── 直接使用指标对应的列进行求和 ──
        sum_cols = [self.col_map[i] for i in self.indicators]

        # ── 逐日求和 ──
        chunk_df['指标和'] = chunk_df[sum_cols].sum(axis=1)

        # 返回处理后的结果
        return chunk_df[['日期', '指标和']]
    
    def process_in_chunks(self) -> pd.DataFrame:
        """分块处理Excel数据"""
        # 1. 读取需求指标
        self.read_demand_indicators()
        
        # 2. 准备读取参数
        data_sheet_name = '原油期权行情历史'  # 第 3 个 sheet 的名称
        usecols = ['日期'] + [self.col_map[i] for i in self.indicators]
        
        # 3. 获取总行数并计算分块数
        total_rows = self.get_total_rows(data_sheet_name)
        num_chunks = (total_rows + self.chunk_size - 1) // self.chunk_size
        print(f"将分为 {num_chunks} 个块进行处理，每块最多 {self.chunk_size} 行")
        
        # 4. 分块处理数据
        result_chunks = []
        
        for i in range(num_chunks):
            start_row = i * self.chunk_size
            # 跳过表头行（如果不是第一块）
            skiprows = start_row if i > 0 else 0
            nrows = min(self.chunk_size, total_rows - start_row)
            
            print(f"正在处理第 {i+1}/{num_chunks} 块 (行 {start_row+1} 到 {start_row+nrows})...")
            
            try:
                # 读取当前块
                chunk_df = pd.read_excel(
                    self.file_path,
                    sheet_name=data_sheet_name,
                    usecols=usecols,
                    skiprows=skiprows,
                    nrows=nrows,
                    parse_dates=['日期']
                )
                
                # 处理当前块
                processed_chunk = self.process_chunk(chunk_df)
                result_chunks.append(processed_chunk)
                
                print(f"第 {i+1} 块处理完成，包含 {len(processed_chunk)} 行数据")
                
            except Exception as e:
                print(f"处理第 {i+1} 块时出错: {e}")
                continue
        
        # 5. 合并所有结果
        print("正在合并所有处理结果...")
        if result_chunks:
            final_result = pd.concat(result_chunks, ignore_index=True)
            # 按日期排序
            final_result = final_result.sort_values('日期').reset_index(drop=True)
            print(f"处理完成！总共 {len(final_result)} 行数据")
            return final_result
        else:
            print("没有成功处理任何数据块")
            return pd.DataFrame()

def main():
    """主函数"""
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return
    
    # 创建处理器实例
    processor = ChunkedExcelProcessor(file_path, chunk_size=5000)  # 可以调整chunk_size
    
    try:
        # 分块处理数据
        result = processor.process_in_chunks()
        
        if not result.empty:
            print("\n处理结果预览:")
            print(result.head(10))
            print(f"\n数据统计:")
            print(f"总行数: {len(result)}")
            print(f"日期范围: {result['日期'].min()} 到 {result['日期'].max()}")
            print(f"指标和统计: 最小值={result['指标和'].min():.2f}, 最大值={result['指标和'].max():.2f}, 平均值={result['指标和'].mean():.2f}")
            
            # 可选：保存结果到文件
            output_path = os.path.join(os.path.dirname(__file__), 'processed_result.csv')
            result.to_csv(output_path, index=False, encoding='utf-8-sig')
            print(f"\n结果已保存到: {output_path}")
        else:
            print("处理失败，没有获得有效结果")
            
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
