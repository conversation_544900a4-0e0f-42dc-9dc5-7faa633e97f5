import pandas as pd

file_path = './原油期权日行情历史数据20250721.xlsx'

# ── 1. 读取需求表首行 ───────────────────────────────
demand_sheet = 0                     # 第 1 个 sheet
row0 = pd.read_excel(file_path, sheet_name=demand_sheet, nrows=1)

# 收集“包含指标*”列
indicators = [
    row0[c].strip()
    for c in row0.columns if c.startswith('包含指标') and pd.notna(row0[c])
]

# ── 2. 指标 ↔ 数据列名映射（按实际列名补全即可） ──
col_map = {
    '成交金额': '成交额（万）',
    '成交量': '成交量',
    '持仓量': '持仓量',
    '行权量': '行权量',
}

# ── 3. 读取第 3 个 sheet「原油期权行情历史」所需列 ──
data_sheet_name = '原油期权行情历史'   # 第 3 个 sheet 的名称
usecols = ['日期'] + [col_map[i] for i in indicators] + ['日成交量(张)']  # 成交量列给换算用

df = (
    pd.read_excel(file_path,
                  sheet_name=data_sheet_name,
                  usecols=usecols,
                  parse_dates=['日期'])
    .sort_values('日期')
)

# ── 4. 如需：成交金额按“成交量(张) → 万”换算后再参与求和 ──
if '成交金额' in indicators:
    df['成交金额_调整'] = df[col_map['成交金额']] * (df['日成交量(张)'] / 1e4)
    sum_cols = ['成交金额_调整'] + [
        col_map[i] for i in indicators if i != '成交金额'
    ]
else:
    sum_cols = [col_map[i] for i in indicators]

# ── 5. 逐日求和 ─────────────────────────────────────
df['指标和'] = df[sum_cols].sum(axis=1)

# 结果：日期 + 求和列
result = df[['日期', '指标和']]
print(result.head()) 
